"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Building2, TrendingDown, TrendingUp } from "lucide-react";
import Image from "next/image";

import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import { CompanyData } from "@/utils/db/dashboard-queries";

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatNumber = (value: number, decimals: number = 2): string => {
  return value.toLocaleString("ro-RO", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

const getReturnColor = (value: number): string => {
  if (value > 0) return "text-green-600 dark:text-green-400";
  if (value < 0) return "text-red-600 dark:text-red-400";
  return "text-gray-600 dark:text-gray-400";
};

export function createCompaniesTableColumns(
  displayCurrency: SupportedCurrency
): ColumnDef<CompanyData>[] {
  return [
    {
      accessorKey: "logo_url",
      header: () => <div className="w-8"></div>,
      cell: ({ row }) => {
        const company = row.original;
        return (
          <div className="flex items-center justify-center">
            <div className="p-1 rounded-md bg-muted">
              {company.logo_url ? (
                <Image
                  src={company.logo_url}
                  alt={company.ticker}
                  className="h-6 w-6"
                  width={24}
                  height={24}
                />
              ) : (
                <Building2 className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 50,
    },
    {
      accessorKey: "company",
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title="Companie"
          isFirstColumn={true}
        />
      ),
      cell: ({ row }) => {
        const company = row.original;
        return (
          <div className="flex flex-wrap gap-2 items-center">
            <div className="font-semibold text-left">{company.company}</div>
            <div className="text-sm text-muted-foreground">
              {company.ticker}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
      size: 200,
    },
    {
      accessorKey: "shares",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Acțiuni" />
        </div>
      ),
      cell: ({ row }) => {
        const shares = row.getValue("shares") as number;
        return (
          <div className="text-right font-medium">
            {formatNumber(shares, 0)}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
    },
    {
      accessorKey: "avgCost",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Preț mediu" />
        </div>
      ),
      cell: ({ row }) => {
        const avgCost = row.getValue("avgCost") as number;
        return (
          <div className="text-right font-medium">
            {formatCurrency(avgCost, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "fiftyTwoWeekLow",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="52W Min" />
        </div>
      ),
      cell: ({ row }) => {
        const company = row.original;
        const low = company.fiftyTwoWeekLow;
        return (
          <div className="text-right font-medium">
            {low
              ? formatCurrency(low, company.currency as SupportedCurrency)
              : "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
    },
    {
      accessorKey: "fiftyTwoWeekHigh",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="52W Max" />
        </div>
      ),
      cell: ({ row }) => {
        const company = row.original;
        const high = company.fiftyTwoWeekHigh;
        return (
          <div className="text-right font-medium">
            {high
              ? formatCurrency(high, company.currency as SupportedCurrency)
              : "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
    },
    {
      accessorKey: "dividendYield",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Div Yield" />
        </div>
      ),
      cell: ({ row }) => {
        const dividendYield = row.original.dividendYield;
        return (
          <div className="text-right font-medium">
            {dividendYield ? formatPercentage(dividendYield) : "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
    },
    {
      accessorKey: "costValue",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Valoare cost" />
        </div>
      ),
      cell: ({ row }) => {
        const costValue = row.getValue("costValue") as number;
        return (
          <div className="text-right font-medium">
            {formatCurrency(costValue, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "marketValue",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Valoare piață" />
        </div>
      ),
      cell: ({ row }) => {
        const marketValue = row.getValue("marketValue") as number;
        return (
          <div className="text-right font-medium">
            {formatCurrency(marketValue, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "dollarReturn",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="$ Return" />
        </div>
      ),
      cell: ({ row }) => {
        const dollarReturn = row.getValue("dollarReturn") as number;
        return (
          <div
            className={`text-right font-medium flex items-center justify-end gap-1 ${getReturnColor(
              dollarReturn
            )}`}
          >
            {dollarReturn > 0 ? (
              <TrendingUp className="h-3 w-3" />
            ) : dollarReturn < 0 ? (
              <TrendingDown className="h-3 w-3" />
            ) : null}
            {formatCurrency(dollarReturn, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "percentReturn",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="% Return" />
        </div>
      ),
      cell: ({ row }) => {
        const percentReturn = row.getValue("percentReturn") as number;
        return (
          <div
            className={`text-right font-medium flex items-center justify-end gap-1 ${getReturnColor(
              percentReturn
            )}`}
          >
            {percentReturn > 0 ? (
              <TrendingUp className="h-3 w-3" />
            ) : percentReturn < 0 ? (
              <TrendingDown className="h-3 w-3" />
            ) : null}
            {formatPercentage(percentReturn)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "yesterdayChange",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Ieri" />
        </div>
      ),
      cell: ({ row }) => {
        const yesterdayChange = row.getValue("yesterdayChange") as number;
        return (
          <div
            className={`text-right font-medium ${getReturnColor(
              yesterdayChange
            )}`}
          >
            {formatCurrency(yesterdayChange, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
    },
    {
      accessorKey: "allocation",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Alocare" />
        </div>
      ),
      cell: ({ row }) => {
        const allocation = row.getValue("allocation") as number;
        return (
          <div className="text-right font-medium">
            {formatPercentage(allocation)}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
    },
    {
      accessorKey: "sector",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Sector" />
      ),
      cell: ({ row }) => {
        const sector = row.getValue("sector") as string;
        return <div className="font-medium">{sector || "N/A"}</div>;
      },
      enableSorting: true,
      size: 150,
    },
    {
      accessorKey: "industry",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Industrie" />
      ),
      cell: ({ row }) => {
        const industry = row.getValue("industry") as string;
        return <div className="font-medium">{industry || "N/A"}</div>;
      },
      enableSorting: true,
      size: 150,
    },
    {
      accessorKey: "country",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Țară" />
      ),
      cell: ({ row }) => {
        const country = row.getValue("country") as string;
        return <div className="font-medium">{country || "N/A"}</div>;
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "dividendIncome",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Dividende" />
        </div>
      ),
      cell: ({ row }) => {
        const dividendIncome = row.getValue("dividendIncome") as number;
        return (
          <div className="text-right font-medium">
            {formatCurrency(dividendIncome, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
    },
  ];
}
