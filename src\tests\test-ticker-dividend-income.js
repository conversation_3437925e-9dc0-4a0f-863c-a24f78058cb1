/**
 * Test suite for calculateTickerDividendIncome function
 * Run with: node src/tests/test-ticker-dividend-income.js
 */

// Mock the required functions
function calculatePortfolioHoldingsAtDate(transactions, targetDate) {
  const holdings = new Map();

  const relevantTransactions = transactions
    .filter((t) => t.transaction_date <= targetDate)
    .sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime()
    );

  relevantTransactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, costBasis: 0 };

    if (transaction.transaction_type === "BUY") {
      const newQuantity = existing.quantity + transaction.quantity;
      const newCostBasis =
        existing.costBasis + transaction.quantity * transaction.price;
      holdings.set(ticker, { quantity: newQuantity, costBasis: newCostBasis });
    } else if (transaction.transaction_type === "SELL") {
      const newQuantity = existing.quantity - transaction.quantity;
      const avgCostPerShare = existing.quantity > 0 ? existing.costBasis / existing.quantity : 0;
      const newCostBasis = newQuantity * avgCostPerShare;
      holdings.set(ticker, { quantity: newQuantity, costBasis: newCostBasis });
    }
  });

  return holdings;
}

function convertAmount(amount, fromCurrency, toCurrency, exchangeRates) {
  if (fromCurrency === toCurrency) {
    return amount;
  }
  
  const rate = exchangeRates.get(`${fromCurrency}${toCurrency}`) || 1;
  return amount * rate;
}

function calculateTickerDividendIncome(
  ticker,
  transactions,
  dividends,
  assetCurrency,
  displayCurrency,
  exchangeRates
) {
  let totalDividendIncome = 0;

  // Filter transactions for this specific ticker
  const tickerTransactions = transactions.filter((t) => t.ticker === ticker);

  dividends.forEach((dividend) => {
    // Calculate holdings at the ex-date
    const holdingsAtExDate = calculatePortfolioHoldingsAtDate(
      tickerTransactions,
      dividend.ex_date
    );

    const holding = holdingsAtExDate.get(ticker);
    if (holding && holding.quantity > 0) {
      // Get currency from dividend's asset relationship or fallback to asset data
      const dividendCurrency = dividend.asset?.currency?.code || assetCurrency;

      // Calculate dividend income in original currency
      const dividendIncomeOriginal =
        holding.quantity * dividend.amount_per_share;

      // Convert to display currency
      const convertedDividendIncome = convertAmount(
        dividendIncomeOriginal,
        dividendCurrency,
        displayCurrency,
        exchangeRates
      );

      totalDividendIncome += convertedDividendIncome;
    }
  });

  return totalDividendIncome;
}

// Test data
const mockTransactions = [
  {
    id: "1",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 10,
    price: 150.00,
    transaction_date: "2024-01-15",
  },
  {
    id: "2",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 5,
    price: 160.00,
    transaction_date: "2024-03-15",
  },
  {
    id: "3",
    ticker: "AAPL",
    transaction_type: "SELL",
    quantity: 3,
    price: 170.00,
    transaction_date: "2024-06-15",
  },
  {
    id: "4",
    ticker: "NVDA",
    transaction_type: "BUY",
    quantity: 20,
    price: 800.00,
    transaction_date: "2024-02-01",
  },
];

const mockDividends = [
  {
    dividend_id: 1,
    asset_id: 213,
    ex_date: "2024-02-01", // Before second AAPL purchase, 10 shares held
    amount_per_share: 0.25,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "AAPL", currency: { code: "USD" } }
  },
  {
    dividend_id: 2,
    asset_id: 213,
    ex_date: "2024-04-01", // After second AAPL purchase, 15 shares held
    amount_per_share: 0.25,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "AAPL", currency: { code: "USD" } }
  },
  {
    dividend_id: 3,
    asset_id: 213,
    ex_date: "2024-07-01", // After AAPL sale, 12 shares held
    amount_per_share: 0.25,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "AAPL", currency: { code: "USD" } }
  },
  {
    dividend_id: 4,
    asset_id: 213,
    ex_date: "2024-01-01", // Before any AAPL purchases, 0 shares held
    amount_per_share: 0.25,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "AAPL", currency: { code: "USD" } }
  },
  {
    dividend_id: 5,
    asset_id: 214,
    ex_date: "2024-03-01", // NVDA dividend, 20 shares held
    amount_per_share: 0.01,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "NVDA", currency: { code: "USD" } }
  },
];

const mockExchangeRates = new Map([
  ["USDEUR", 0.85], // 1 USD = 0.85 EUR
  ["EURUSD", 1.18], // 1 EUR = 1.18 USD
]);

// Test functions
function assertEqual(actual, expected, testName, tolerance = 0.01) {
  if (Math.abs(actual - expected) < tolerance) {
    console.log(`✅ PASS: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return false;
  }
}

// Run tests
console.log("🧪 Testing calculateTickerDividendIncome function\n");

// Test 1: AAPL dividend income calculation
console.log("Test 1: AAPL dividend income (USD to EUR conversion)");
const aaplDividends = mockDividends.filter(d => d.asset.ticker === "AAPL");
const aaplIncome = calculateTickerDividendIncome(
  "AAPL",
  mockTransactions,
  aaplDividends,
  "USD",
  "EUR",
  mockExchangeRates
);
// Expected: (0 * 0.25 + 10 * 0.25 + 15 * 0.25 + 12 * 0.25) * 0.85 = 9.25 * 0.85 = 7.8625
const expectedAapl = 7.8625;
assertEqual(aaplIncome, expectedAapl, "AAPL dividend income calculation");

// Test 2: NVDA dividend income calculation
console.log("\nTest 2: NVDA dividend income (USD to EUR conversion)");
const nvdaDividends = mockDividends.filter(d => d.asset.ticker === "NVDA");
const nvdaIncome = calculateTickerDividendIncome(
  "NVDA",
  mockTransactions,
  nvdaDividends,
  "USD",
  "EUR",
  mockExchangeRates
);
// Expected: 20 * 0.01 * 0.85 = 0.17
const expectedNvda = 0.17;
assertEqual(nvdaIncome, expectedNvda, "NVDA dividend income calculation");

// Test 3: Same currency (no conversion)
console.log("\nTest 3: Same currency calculation (USD to USD)");
const aaplIncomeUSD = calculateTickerDividendIncome(
  "AAPL",
  mockTransactions,
  aaplDividends,
  "USD",
  "USD",
  mockExchangeRates
);
// Expected: 0 + 2.5 + 3.75 + 3.0 = 9.25
const expectedAaplUSD = 9.25;
assertEqual(aaplIncomeUSD, expectedAaplUSD, "AAPL dividend income (no conversion)");

// Test 4: No dividends for ticker
console.log("\nTest 4: No dividends for ticker");
const noIncome = calculateTickerDividendIncome(
  "TSLA",
  mockTransactions,
  mockDividends,
  "USD",
  "EUR",
  mockExchangeRates
);
assertEqual(noIncome, 0, "No dividends for non-existent ticker");

// Test 5: Empty dividends array
console.log("\nTest 5: Empty dividends array");
const emptyDividends = calculateTickerDividendIncome(
  "AAPL",
  mockTransactions,
  [],
  "USD",
  "EUR",
  mockExchangeRates
);
assertEqual(emptyDividends, 0, "Empty dividends array");

// Test 6: Empty transactions array
console.log("\nTest 6: Empty transactions array");
const emptyTransactions = calculateTickerDividendIncome(
  "AAPL",
  [],
  aaplDividends,
  "USD",
  "EUR",
  mockExchangeRates
);
assertEqual(emptyTransactions, 0, "Empty transactions array");

// Test 7: Dividend before any holdings
console.log("\nTest 7: Dividend before any holdings");
const earlyDividend = [
  {
    dividend_id: 99,
    asset_id: 213,
    ex_date: "2023-12-01", // Before any transactions
    amount_per_share: 1.00,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "AAPL", currency: { code: "USD" } }
  }
];
const earlyIncome = calculateTickerDividendIncome(
  "AAPL",
  mockTransactions,
  earlyDividend,
  "USD",
  "EUR",
  mockExchangeRates
);
assertEqual(earlyIncome, 0, "Dividend before any holdings");

// Test 8: Missing exchange rate (fallback to 1:1)
console.log("\nTest 8: Missing exchange rate");
const emptyRates = new Map();
const noRateIncome = calculateTickerDividendIncome(
  "AAPL",
  mockTransactions,
  [aaplDividends[1]], // One dividend: 15 * 0.25 = 3.75
  "USD",
  "EUR",
  emptyRates
);
assertEqual(noRateIncome, 3.75, "Missing exchange rate (1:1 fallback)");

console.log("\n🏁 Test suite completed!");
